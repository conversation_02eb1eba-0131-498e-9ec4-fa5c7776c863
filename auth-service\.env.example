# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=secret-passworrd
DB_DIALECT=postgres
DB_SCHEMA=auth

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# Bcrypt Configuration
BCRYPT_ROUNDS=12

# Default Token Balance for New Users
DEFAULT_TOKEN_BALANCE=5

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/auth-service.log
